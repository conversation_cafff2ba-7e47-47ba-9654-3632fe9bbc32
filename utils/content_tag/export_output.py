import os
import boto3
from pyathena import connect
import pandas as pd
from datetime import datetime
from io import StringIO
import json

CUSTOMER = ""
AWS_REGION = ""
DEV_SUFFIX = ""
SCENARIOUIDS = "default,default2,default3,default4"
SCHEMA_NAME = "impact_"+CUSTOMER+"_satya"
S3_STAGING_DIR = ""

AWS_ACCESS_KEY = os.environ.get('AWS_ACCESS_KEY_ID')
AWS_SECRET_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
AWS_SESSION_TOKEN = os.environ.get('AWS_SESSION_TOKEN')

def get_s3staging(customer, region, devsuffix):
    s3_staging_dir = "s3://aktana-bdp" + region + "-glue/athena/" + customer + '/' + devsuffix
    return s3_staging_dir

def get_s3target(customer, region, suffix):
    if suffix == 'prod':
        s3target = f"s3://aktana-bdp-{customer}/prod/dbt/impact"
    else:
        s3target = f"s3://aktana-bdp{region}-glue/dbt/{customer}/{suffix}"

    return s3target

def get_s3_export_path(customer, environment):
    now = datetime.now()
    current_date = now.date()
    s3target = f"s3://aktana-externalfiles-{customer}/{environment}/outgoing/contenttagging/runDate={current_date}"
    return s3target

def get_export_s3_bucket(customer):
    s3_bucket = f"aktana-externalfiles-{customer}"
    return s3_bucket

def get_export_s3_file_path(environment):
    now = datetime.now()
    current_date = now.date()
    s3_file_path = f"{environment}/outgoing/contenttagging/runDate={current_date}/taggedAssets.csv"
    return s3_file_path

def get_schema(customer, suffix):
    schema = "impact_" + customer + "_" + suffix
    return schema

def get_table(conn, sql):
    df = pd.read_sql_query(sql, conn)
    return df

# def get_export_flag_file_path(customer, environment):
#     file_path = f"s3://aktana-bdp-{customer}/{environment}/tempdata/MSRScenario/default.json"
#     return file_path

def get_export_flag_file_bucket(customer):
    bucket = f"aktana-bdp-{customer}"
    return bucket

def get_export_flag_file_path(environment):
    file_path = f"{environment}/tempdata/MSRScenario/default.json"
    return file_path

def main():
    customer = os.environ.get('CUSTOMER')
    awsregion = os.environ.get('AWS_REGION')
    environment = os.environ.get('ENVIRONMENT')
    devsuffix = os.environ.get('SCHEMA_SUFFIX', environment)
    region = os.environ.get('REGION')
    action="RUN_MODEL"

    print('CUSTOMER    :', customer)
    print('AWSREGION   :', awsregion)
    print('ACTION      :', action)
    print('ENVIRONMENT :', environment)
    print('DEVSUFFIX   :', devsuffix)

    s3staging = get_s3staging(customer, region, devsuffix or environment)
    s3target = get_s3target(customer, region, devsuffix or environment)
    srcschema = get_schema(customer, devsuffix)
    tgtschema = get_schema(customer, devsuffix)

    print('S3STAGING   :', s3staging)
    print('S3TARGET    :', s3target)
    print('SRCSCHEMA   :', srcschema)
    print('TGTSCHEMA   :', tgtschema)

    boto3.setup_default_session(region_name=awsregion)

    global conn
    conn = connect(aws_access_key_id=AWS_ACCESS_KEY,
                   aws_secret_access_key=AWS_SECRET_KEY,
                   aws_session_token=AWS_SESSION_TOKEN,
                   s3_staging_dir=s3staging,
                   region_name=awsregion,
                   schema_name=srcschema,
                   )

    export_flag_file_bucket = get_export_flag_file_bucket(customer)
    export_flag_file_path = get_export_flag_file_path(environment)

    s3_client = boto3.client('s3')

    export_flag = False
    try:
        response = s3_client.get_object(Bucket=export_flag_file_bucket, Key=export_flag_file_path)
        json_content = response['Body'].read().decode('utf-8')

        # Parse the JSON content
        json_data = json.loads(json_content)
        # print(json_data)

        # Check for the particular key
        key_to_check = "ctag_export_output_csv"
        if key_to_check in json_data:
            print(f"The key '{key_to_check}' exists in the JSON file.")
            print(f"Value: {json_data[key_to_check]}")
            export_flag = json_data[key_to_check]
        else:
            print(f"The key '{key_to_check}' does not exist in the JSON file.")
            export_flag = False

    except Exception as e:
        print(f"Error occurred: {e}")
        export_flag = False

    if export_flag:
        global export_output_df
        export_output_df = get_table(conn, "select * from ctag_output_export_v")

        print(export_output_df)

        output_csv_buffer = StringIO()
        export_output_df.to_csv(output_csv_buffer, index=False)

        s3_bucket = get_export_s3_bucket(customer)
        s3_file_path = get_export_s3_file_path(environment)
        try:
            s3_client.put_object(Bucket=s3_bucket, Key=s3_file_path, Body=output_csv_buffer.getvalue())
            print(f"DataFrame successfully saved to s3://{s3_bucket}/{s3_file_path}")
        except Exception as e:
            print(f"Error occurred: {e}")
    else:
        print("Flag missing or false to export csv output to s3. Nothing written to s3!")


if __name__ == "__main__":
    main()