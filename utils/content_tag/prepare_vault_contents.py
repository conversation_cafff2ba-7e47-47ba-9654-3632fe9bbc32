import os
import boto3
from pyathena import connect
import pandas as pd
import numpy as np
import pytesseract
from PIL import Image
from datetime import datetime
from bs4 import BeautifulSoup
import awswrangler as wr
import json
from pdf2image import convert_from_path
from PyPDF2 import PdfReader
from transformers import BartTokenizer, BartForConditionalGeneration
from botocore.exceptions import ClientError
import zipfile


CUSTOMER = ""
AWS_REGION = ""
DEV_SUFFIX = ""
SCENARIOUIDS = "default,default2,default3,default4"
SCHEMA_NAME = "impact_"+CUSTOMER+"_satya"
S3_STAGING_DIR = ""

AWS_ACCESS_KEY = os.environ.get('AWS_ACCESS_KEY_ID')
AWS_SECRET_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
AWS_SESSION_TOKEN = os.environ.get('AWS_SESSION_TOKEN')

tokenizer = BartTokenizer.from_pretrained("facebook/bart-large-cnn")
model = BartForConditionalGeneration.from_pretrained("facebook/bart-large-cnn")

def summarize_content(content):
    # Check if content is empty or None before summarizing
    if not content.strip():
        print("Empty content, skipping summary.")
        return ""  # Return empty summary for empty content

    # Proceed with summarization for non-empty content
    inputs = tokenizer.encode(content, return_tensors="pt", max_length=1024, truncation=True)
    outputs = model.generate(
        inputs,
        max_length=50,  # Adjusted to aim for a 15-20 word summary
        min_length=15,  # Minimum length of the summary to ensure 15-20 words
        length_penalty=2.0,
        num_beams=4,
        early_stopping=True
    )

    summary = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return summary

def get_s3staging(customer, region, devsuffix):
    """
    Constructs the S3 staging directory path.
    """
    try:
        s3_staging_dir = f"s3://aktana-bdp{region}-glue/athena/{customer}/{devsuffix}"
        return s3_staging_dir
    except Exception as e:
        print(f"Error constructing S3 staging directory: {e}")
        return None  # Return None or any fallback value

def get_s3target(customer, region, suffix):
    """
    Constructs the S3 target directory path based on the environment.
    """
    try:
        if suffix == 'prod':
            s3target = f"s3://aktana-bdp-{customer}/prod/dbt/impact"
        else:
            s3target = f"s3://aktana-bdp{region}-glue/dbt/{customer}/{suffix}"
        return s3target
    except Exception as e:
        print(f"Error constructing S3 target directory: {e}")
        return None  # Return None or any fallback value

def get_s3_export_path(customer, environment):
    """
    Constructs the S3 export path with the current date.
    """
    try:
        now = datetime.now()
        current_date = now.date()
        s3target = f"s3://aktana-externalfiles-{customer}/{environment}/outgoing/contenttagging/runDate={current_date}"
        return s3target
    except Exception as e:
        print(f"Error constructing S3 export path: {e}")
        return None  # Return None or any fallback value

def get_s3_bucket(customer):
    """
    Constructs the S3 bucket path for the customer.
    """
    try:
        s3_bucket = f"aktana-externalfiles-{customer}"
        return s3_bucket
    except Exception as e:
        print(f"Error constructing S3 bucket: {e}")
        return None  # Return None or any fallback value

def get_export_s3_file_path(environment):
    """
    Constructs the export S3 file path with the current date.
    """
    try:
        now = datetime.now()
        current_date = now.date()
        s3_file_path = f"{environment}/outgoing/contenttagging/runDate={current_date}/taggedAssets.csv"
        return s3_file_path
    except Exception as e:
        print(f"Error constructing export S3 file path: {e}")
        return None  # Return None or any fallback value

def get_schema(customer, suffix):
    """
    Constructs the database schema name.
    """
    try:
        schema = f"impact_{customer}_{suffix}"
        return schema
    except Exception as e:
        print(f"Error constructing schema: {e}")
        return None  # Return None or any fallback value

def get_table(conn, sql):
    """
    Executes a SQL query and returns the result as a DataFrame.
    In case of an error, it returns an empty DataFrame and logs the error.
    """
    try:
        df = pd.read_sql_query(sql, conn)
        return df
    except Exception as e:
        print(f"Error occurred while fetching table: {e}")
        return pd.DataFrame()  # Return an empty DataFrame

def scrape_html(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
        soup = BeautifulSoup(content, 'html.parser')
        text = soup.get_text(separator='\n')
        # Split the text into lines and filter out empty lines
        lines = (line.strip() for line in text.splitlines())
        cleaned_text = '\n'.join(line for line in lines if line)

        return cleaned_text

def extract_text_from_image(file_path):
    img = Image.open(file_path)
    text = pytesseract.image_to_string(img)

    # Clean up the text (remove extra spaces and newlines)
    cleaned_text = ' '.join(text.split())

    return cleaned_text

def is_valid_pdf(file_path):
    try:
        with open(file_path, 'rb') as f:
            reader = PdfReader(f)
            if len(reader.pages) > 0:
                return True
    except Exception as e:
        print(f"Invalid PDF file: {file_path}, Error: {e}")
    return False

def extract_text_from_pdf(file_path):
    try:
        pages = convert_from_path(file_path)
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []  # Return an empty list for this file

    text_by_page = []
    for i, page in enumerate(pages):
        temp_image_path = f'temp_page_{i}.png'
        page.save(temp_image_path, 'PNG')
        cleaned_text = extract_text_from_image(temp_image_path)
        text_by_page.append(f"{cleaned_text}")
        os.remove(temp_image_path)

    return text_by_page  # Return a list of page texts

def get_checksum_from_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
        try:
            data = json.loads(content)
            key_to_find = 'md5checksum__v'
            value = data.get(key_to_find, '')
            return value
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON: {e}")
            return ''

def list_files_in_s3_folder(bucket, folder_prefix):
    """
    List all files in the specified S3 folder.

    Args:
        bucket (str): The name of the S3 bucket.
        folder_prefix (str): The prefix (folder path) in the S3 bucket.

    Returns:
        list: A list of file keys in the specified folder.
    """
    try:
        response = s3_client.list_objects_v2(Bucket=bucket, Prefix=folder_prefix)
        # Check if 'Contents' is in the response
        if 'Contents' in response:
            return [obj['Key'] for obj in response['Contents']]
        else:
            print(f"No files found in {folder_prefix} of bucket {bucket}")
            return []
    except ClientError as e:
        print(f"Error listing files in S3 folder {folder_prefix} in bucket {bucket}: {e}")
        return []


def download_file_from_s3(bucket, s3_key, local_path):
    try:
        s3_client.download_file(bucket, s3_key, local_path)
        print(f"Successfully downloaded {s3_key} to {local_path}")
    except ClientError as e:
        if e.response['Error']['Code'] == '404':
            pass
        else:
            print(f"Error occurred: {e}")

def extract_content_from_zip(zip_file_path, download_dir='/tmp'):
    """
    Extracts files from a ZIP archive and returns the paths of the extracted files.
    """
    extracted_files = []
    try:
        with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
            zip_ref.extractall(download_dir)
            for file_name in zip_ref.namelist():
                if file_name.endswith('/'):
                    continue  # Skip directories within the ZIP
                extracted_file_path = os.path.join(download_dir, file_name)
                extracted_files.append(extracted_file_path)
                print(f"Extracted file: {extracted_file_path}")  # Debugging log
    except Exception as e:
        print(f"Error extracting {zip_file_path}: {e}")
    return extracted_files

def process_zip_file(zip_file_path, download_dir='/tmp'):
    """
    Processes a ZIP file by extracting its contents and processing the HTML and PDF files within it.
    """
    extracted_files = extract_content_from_zip(zip_file_path, download_dir)
    combined_text_content = []

    for extracted_file in extracted_files:
        try:
            if extracted_file.lower().endswith('.html'):
                combined_text_content.append({
                    'content': scrape_html(extracted_file),
                    'page_number': 1,
                    'content_type': 'html'
                })
                print(f"Processed HTML file in ZIP: {extracted_file}")  # Debugging log
            elif extracted_file.lower().endswith('.pdf'):
                page_contents = extract_text_from_pdf(extracted_file)
                for i, text in enumerate(page_contents):
                    combined_text_content.append({
                        'content': text,
                        'page_number': i + 1,
                        'content_type': 'pdf'
                    })
                print(f"Processed PDF file in ZIP: {extracted_file}")  # Debugging log
            elif extracted_file.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff', '.bmp')):
                combined_text_content.append({
                    'content': extract_text_from_image(extracted_file),
                    'page_number': 1,
                    'content_type': 'image'
                })
                print(f"Processed image file in ZIP: {extracted_file}")  # Debugging log
        except Exception as e:
            print(f"Error processing file inside ZIP: {extracted_file}: {e}")

    return combined_text_content


def scrape_files_from_s3(bucket, s3_folders, download_dir='/tmp'):
    """
    Downloads and scrapes content from S3. Handles HTML, images, PDFs, and ZIP files.
    """
    all_scraped_contents = []

    for folder_prefix in s3_folders:
        s3_keys = list_files_in_s3_folder(bucket, folder_prefix)
        combined_content = []  # Temporary list to hold content for a single vault ID
        for s3_key in s3_keys:
            if not s3_key.endswith(('.html', '.png', '.pdf', '.pptx', '.zip')):
                print("Folder found, skipping")  # Skip folders
                continue
            file_name = s3_key.split('/')[-1]
            local_path = os.path.join(download_dir, file_name)
            print(f"Downloading file from S3: {local_path}")
            try:
                download_file_from_s3(bucket, s3_key, local_path)

                content_type = None

                if file_name.lower().endswith('.html'):
                    text = scrape_html(local_path)
                    content_type = 'html'
                    combined_content.append({
                        'content': text,
                        'page_number': 1,
                        'content_type': content_type
                    })
                    print(f"Scraped HTML content from {file_name}")
                elif file_name.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff', '.bmp')):
                    text = extract_text_from_image(local_path)
                    content_type = 'image'
                    combined_content.append({
                        'content': text,
                        'page_number': 1,
                        'content_type': content_type
                    })
                    print(f"Extracted text from image {file_name} using OCR")
                elif file_name.lower().endswith('.pdf'):
                    if is_valid_pdf(local_path):
                        page_contents = extract_text_from_pdf(local_path)
                        content_type = 'pdf'
                        for i, text in enumerate(page_contents):
                            combined_content.append({
                                'content': text,
                                'page_number': i + 1,
                                'content_type': content_type
                            })
                        print(f"Extracted text from PDF {file_name}")
                    else:
                        print(f"Skipping invalid PDF file: {file_name}")
                elif file_name.lower().endswith('.zip'):
                    zip_content = process_zip_file(local_path, download_dir)
                    combined_content.extend(zip_content)  # Add all extracted contents to combined content
                    print(f"Processed ZIP file {file_name}")
                else:
                    print(f"Unsupported file format: {file_name}")
            except FileNotFoundError:
                pass
            except Exception as e:
                print(f"Error processing {file_name}: {e}")

            if os.path.exists(local_path):
                os.remove(local_path)

        all_scraped_contents.append(combined_content)
    return all_scraped_contents

def get_new_docs_to_scrape(required_vault_data, existing_vault_data):
    """"this functions takes input of required vault ids from query and existing vault ids from vault_content table
     then returns all required vaults ids which are not present in exisintg vault ids from vault content table"""
    if existing_vault_data.empty:
        return set(required_vault_data['cms_messageuid'].tolist())

    required_cms_messageuids = required_vault_data['cms_messageuid'].astype(str).values
    existing_cms_messageuids = existing_vault_data['cms_messageuid'].astype(str).values

    new_docs_to_scrape = [uid for uid in required_cms_messageuids if uid not in existing_cms_messageuids]

    return set(new_docs_to_scrape)

def prepare_s3_folder_location(environment, docs_ids):
    folder_paths = []
    for doc_id in docs_ids:
        folder_path = f"{environment}/content/{doc_id}/content"
        folder_paths.append(folder_path)
    return folder_paths


def get_checksum_val(s3_bucket, s3_folders, download_dir='/tmp'):
    """
    Checks for checksum from metadata.txt files and returns an array of checksums in the incoming order of vault ids.
    Returns '' empty string for checksum not found.
    """
    checksums = []

    for folder_prefix in s3_folders:
        print("folder prefix:", folder_prefix)

        # Define the two possible paths for metadata.txt
        key = folder_prefix + "/metadata.txt"
        key2 = folder_prefix.removesuffix("/content") + "/metadata.txt"

        try:
            local_path = os.path.join(download_dir, "metadata.txt")

            # First try to download using the original key (key)
            try:
                download_file_from_s3(s3_bucket, key, local_path)
                checksum = get_checksum_from_file(local_path)
                checksums.append(checksum)
                print(f"Checksum found in: {key}")
            except Exception as e1:
                print(f"File not found in {key}, trying {key2} instead.")

                # Try downloading with key2 if key fails
                try:
                    download_file_from_s3(s3_bucket, key2, local_path)
                    checksum = get_checksum_from_file(local_path)
                    checksums.append(checksum)
                    print(f"Checksum found in: {key2}")
                except Exception as e2:
                    print(f"Checksum not found in both {key} and {key2}.")
                    checksums.append('')  # If both fail, append empty string

            # Clean up local file after checking
            if os.path.exists(local_path):
                os.remove(local_path)

        except Exception as e:
            print(f"Error processing {folder_prefix}: {e}")
            checksums.append('')

    return checksums

def download_and_scrape_docs(customer, environment, docs_ids_to_scrape, product_vod_c_list):
    """
    Downloads and scrapes content from S3 based on document IDs and adds a content type field.
    """
    s3_folders = prepare_s3_folder_location(environment, docs_ids_to_scrape)
    s3_bucket = get_s3_bucket(customer)
    contents = scrape_files_from_s3(s3_bucket, s3_folders)
    checksums = get_checksum_val(s3_bucket, s3_folders)

    data = []
    for doc_id, product_vod_c, content_pages, checksum in zip(docs_ids_to_scrape, product_vod_c_list, contents, checksums):
        if isinstance(content_pages, list):
            for page_content in content_pages:
                data.append({
                    'cms_messageuid': doc_id,
                    'product_vod_c': product_vod_c,
                    'page_number': page_content['page_number'],
                    'content': page_content['content'],
                    'content_summary': summarize_content(page_content['content']),
                    'checksum': checksum,
                    'content_type': page_content['content_type'],  # Add content_type field
                })
        else:
            # Handling HTML content separately
            if 'content' in content_pages:
                data.append({
                    'cms_messageuid': doc_id,
                    'product_vod_c': product_vod_c,
                    'page_number': 1,
                    'content': content_pages['content'],
                    'content_summary': summarize_content(content_pages['content']),
                    'checksum': checksum,
                    'content_type': content_pages['content_type'],  # Add content_type field
                })
            else:
                print(f"No 'content' key found in content_pages: {content_pages}")

    df = pd.DataFrame(data)
    df = df.drop_duplicates()

    # Cast the necessary columns to string
    for column in ['cms_messageuid', 'product_vod_c', 'page_number']:
        if column in df.columns:
            df[column] = df[column].astype('string')

    print(f"Length of docs_ids_to_scrape: {len(docs_ids_to_scrape)}")
    print(f"Length of contents: {len(contents)}")
    print(f"Length of checksums: {len(checksums)}")
    print(f"DataFrame length: {len(df)}")

    return df

def save_result_to_s3(s3target, tgtschema, scraped_content_df):
    """
    Saves the scraped content to S3 in Parquet format.
    """
    # Cast columns to appropriate types
    scraped_content_df['cms_messageuid'] = scraped_content_df['cms_messageuid'].astype('string')
    scraped_content_df['product_vod_c'] = scraped_content_df['product_vod_c'].astype('string')
    scraped_content_df['page_number'] = scraped_content_df['page_number'].astype('int')
    scraped_content_df['content'] = scraped_content_df['content'].astype('string')
    scraped_content_df['content_summary'] = scraped_content_df['content_summary'].astype('string')
    scraped_content_df['checksum'] = scraped_content_df['checksum'].astype('string')
    scraped_content_df['content_type'] = scraped_content_df['content_type'].astype('string')  # Ensure content_type is stored as string

    # Storing data on Data Lake
    wr.s3.to_parquet(df=scraped_content_df, mode="overwrite",
                     path=s3target + "/CTAG_VAULT_CONTENT/", dataset=True, database=tgtschema, table="ctag_vault_content")

    print('Vault content written to S3')
    return

def get_existing_docs_to_update(customer, environment, existing_vault_data):
    if existing_vault_data.empty or len(existing_vault_data) == 0:
        return []
    s3_bucket = get_s3_bucket(customer)

    existing_vault_ids = existing_vault_data['cms_messageuid'].values
    existing_checksum_vals_in_table = existing_vault_data['checksum'].values

    s3_folders = prepare_s3_folder_location(environment, existing_vault_ids)
    checksum_vals_in_vault_files = get_checksum_val(s3_bucket, s3_folders)

    print("existing in tables")
    print(existing_checksum_vals_in_table)
    print("exisitng in files")
    print(checksum_vals_in_vault_files)

    mismatched_cms_messageuid = existing_vault_data.loc[
        existing_vault_data['checksum'] != checksum_vals_in_vault_files, 'cms_messageuid'
    ].to_numpy()

    unique_mismatched_cms_messageuid = np.unique(mismatched_cms_messageuid)

    return unique_mismatched_cms_messageuid

def update_scraped_content(existing_vault_data, update_scraped_content_df):
    if existing_vault_data.empty and update_scraped_content_df.empty:
        return pd.DataFrame(columns=['cms_messageuid', 'content', 'checksum'])

    # If existing_vault_data is empty, return update_scraped_content_df
    if existing_vault_data.empty:
        return update_scraped_content_df

    # If update_scraped_content_df is empty, return existing_vault_data
    if update_scraped_content_df.empty:
        return existing_vault_data

    # Find the cms_messageuid values in update_scraped_content_df
    update_cms_messageuids = update_scraped_content_df['cms_messageuid'].values

    # Identify rows in existing_vault_data whose cms_messageuid is not in update_scraped_content_df
    non_matching_rows = existing_vault_data[~existing_vault_data['cms_messageuid'].isin(update_cms_messageuids)]

    # Combine non-matching rows from existing_vault_data with all rows from update_scraped_content_df
    result_df = pd.concat([non_matching_rows, update_scraped_content_df], ignore_index=True)

    return result_df

def update_scraped_content(existing_vault_data, update_scraped_content_df):
    if existing_vault_data.empty and update_scraped_content_df.empty:
        return pd.DataFrame(columns=[ 'cms_messageuid','product_vod_c', 'page_number', 'content', 'content_summary', 'checksum'])

    if existing_vault_data.empty:
        return update_scraped_content_df

    if update_scraped_content_df.empty:
        return existing_vault_data

    update_keys = update_scraped_content_df[['cms_messageuid', 'page_number']].apply(tuple, axis=1).values
    existing_keys = existing_vault_data[['cms_messageuid', 'page_number']].apply(tuple, axis=1).values
    non_matching_rows = existing_vault_data[~existing_vault_data[['cms_messageuid', 'page_number']].apply(tuple, axis=1).isin(update_keys)]

    result_df = pd.concat([non_matching_rows, update_scraped_content_df], ignore_index=True)
    return result_df


def main():
    try:
        # Fetch environment variables
        customer = os.environ.get('CUSTOMER')
        awsregion = os.environ.get('AWS_REGION')
        environment = os.environ.get('ENVIRONMENT')
        devsuffix = os.environ.get('SCHEMA_SUFFIX', environment)
        region = os.environ.get('REGION')
        action = "RUN_MODEL"

        # Print environment details
        print(f'CUSTOMER    : {customer}')
        print(f'AWSREGION   : {awsregion}')
        print(f'ACTION      : {action}')
        print(f'ENVIRONMENT : {environment}')
        print(f'DEVSUFFIX   : {devsuffix}')

        # S3 and schema setup
        s3staging = get_s3staging(customer, region, devsuffix or environment)
        s3target = get_s3target(customer, region, devsuffix or environment)
        srcschema = get_schema(customer, devsuffix)
        tgtschema = get_schema(customer, devsuffix)

        print(f'S3STAGING   : {s3staging}')
        print(f'S3TARGET    : {s3target}')
        print(f'SRCSCHEMA   : {srcschema}')
        print(f'TGTSCHEMA   : {tgtschema}')

        # Boto3 session setup
        boto3.setup_default_session(region_name=awsregion)

        # Establish Athena connection
        global conn
        conn = connect(
            aws_access_key_id=AWS_ACCESS_KEY,
            aws_secret_access_key=AWS_SECRET_KEY,
            aws_session_token=AWS_SESSION_TOKEN,
            s3_staging_dir=s3staging,
            region_name=awsregion,
            schema_name=srcschema,
        )

        # Setup S3 client
        global s3_client
        s3_client = boto3.client('s3')

        # Query required vault data
        print("Fetching required vault data...")
        # required_vault_data = get_table(conn, """
        #     SELECT c.product_vod__c AS productuid, km.vault_doc_id_vod__c AS cms_messageuid, COUNT(*) AS document_count
        #     FROM call2_key_message_vod__c_v c
        #     INNER JOIN key_message_vod__c_v km
        #     ON c.key_message_vod__c = km.Id AND km.vault_doc_id_vod__c IS NOT NULL
        #     GROUP BY c.product_vod__c, km.vault_doc_id_vod__c
        # """)
        required_vault_data = get_table(conn, sql="SELECT * FROM ctag_messages_recently_used")
        print(f"Required vault data columns: {required_vault_data.columns}")
        print(f"Required vault data sample:\n{required_vault_data}")

        # Query existing vault content
        print("Fetching existing vault content data...")
        existing_vault_data = get_table(conn, "SELECT * FROM ctag_vault_content")
        print(f"Existing vault data sample:\n{existing_vault_data}")

        # Find existing docs to update
        print("Identifying documents that need updating...")
        existing_docs_to_update = get_existing_docs_to_update(customer, environment, existing_vault_data)
        # existing_docs_to_update=['495729','656152']
        print(f"Documents to update: {existing_docs_to_update}")

        # Download and scrape updated docs
        print("Downloading and scraping content of updated documents...")
        product_vod_c_list = required_vault_data['productuid'].tolist()
        update_scraped_content_df = download_and_scrape_docs(customer, environment, existing_docs_to_update,
                                                             product_vod_c_list)
        print(f"Scraped content for updates:\n{update_scraped_content_df.head()}")

        # Combine with existing vault data
        print("Updating existing scraped content with new content...")
        final_scraped_content_df = update_scraped_content(existing_vault_data, update_scraped_content_df)
        print(f"Updated scraped content:\n{final_scraped_content_df.head()}")

        # Find new docs to scrape
        print("Identifying new documents that need scraping...")
        new_docs_to_scrape = get_new_docs_to_scrape(required_vault_data, existing_vault_data)
        # new_docs_to_scrape=[]
        print(f"New documents to scrape: {new_docs_to_scrape}")

        # Download and scrape new docs
        print("Downloading and scraping content of new documents...")
        new_scraped_content_df = download_and_scrape_docs(customer, environment, new_docs_to_scrape, product_vod_c_list)
        print(f"Scraped content for new documents:\n{new_scraped_content_df.head()}")

        # Combine all scraped content
        print("Combining updated and new scraped content...")
        final_scraped_content_df = pd.concat([final_scraped_content_df, new_scraped_content_df], ignore_index=True)
        final_scraped_content_df = final_scraped_content_df.drop_duplicates()
        print(f"Final combined scraped content:\n{final_scraped_content_df}")
        # Save final result to S3
        print("Saving scraped content to S3...")
        save_result_to_s3(s3target, tgtschema, final_scraped_content_df)
        print("Scraped content successfully saved to S3.")

    except Exception as e:
        print(f"An error occurred: {e}")
        raise  # Re-raise exception for debugging

if __name__ == "__main__":
    main()


