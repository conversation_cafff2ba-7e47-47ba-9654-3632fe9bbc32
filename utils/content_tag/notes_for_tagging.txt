
message_df -> ctag_messages_recently_used
product_df -> message_df.groupby(['productname','productuid']).size()
docprod_df = message_df.groupby(['document_type','productuid','productname']).size()

for loop starting from line 524 ---
for each row in docprod_df (doc_type + product comb.):
    for each pre-defined tag:
        find top 30 relevant docs
            for each doc:
                if doc_type and product match