{{ config(
    materialized='table',
    format='parquet'
) }}

{%- set source_relation = adapter.get_relation(
      database=target.database,
      schema=target.schema,
      identifier='ctag_vault_content') -%}

{{ log(source_relation, info=True) }}

{% set table_exists=source_relation is not none   %}

{% if table_exists %}
{{ log("ctag vault content Table exists", info=True) }}

SELECT
    cms_messageuid,
    LISTAGG(content, ' ') WITHIN GROUP (ORDER BY content) as content
FROM {{ source_relation }}
GROUP By cms_messageuid

{% else %}
{{ log("ctag vault content Table does not exist, creating empty table", info=True) }}

SELECT
    CAST(null AS VARCHAR) as cms_messageuid,
    CAST(null AS VARCHAR) as content
WHERE false
{% endif %}